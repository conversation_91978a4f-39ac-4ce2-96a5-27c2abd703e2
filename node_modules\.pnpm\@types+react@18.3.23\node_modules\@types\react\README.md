# Installation
> `npm install --save @types/react`

# Summary
This package contains type definitions for react (https://react.dev/).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react/v18.

### Additional Details
 * Last updated: <PERSON><PERSON>, 27 May 2025 08:02:50 GMT
 * Dependencies: [@types/prop-types](https://npmjs.com/package/@types/prop-types), [csstype](https://npmjs.com/package/csstype)

# Credits
These definitions were written by [<PERSON><PERSON>](https://asana.com), [AssureSign](http://www.assuresign.com), [Microsoft](https://microsoft.com), [<PERSON>](https://github.com/johnnyreilly), [<PERSON><PERSON> Benezech](https://github.com/bbenezech), [<PERSON><PERSON><PERSON>](https://github.com/p<PERSON><PERSON><PERSON><PERSON>), [<PERSON>](https://github.com/erica<PERSON>on), [<PERSON><PERSON><PERSON>](https://github.com/<PERSON><PERSON><PERSON>), [<PERSON>](https://github.com/theruther4d), [<PERSON><PERSON><PERSON><PERSON>](https://github.com/guilhermehubner), [Ferdy Budhidharma](https://github.com/ferdaber), [Johann Rakotoharisoa](https://github.com/jrakotoharisoa), [Olivier Pascal](https://github.com/pascaloliv), [Martin Hochel](https://github.com/hotell), [Frank Li](https://github.com/franklixuefei), [Jessica Franco](https://github.com/Jessidhia), [Saransh Kataria](https://github.com/saranshkataria), [Kanitkorn Sujautra](https://github.com/lukyth), [Sebastian Silbermann](https://github.com/eps1lon), [Kyle Scully](https://github.com/zieka), [Cong Zhang](https://github.com/dancerphil), [Dimitri Mitropoulos](https://github.com/dimitropoulos), [JongChan Choi](https://github.com/disjukr), [Victor Magalhães](https://github.com/vhfmag), [Priyanshu Rav](https://github.com/priyanshurav), [Dmitry Semigradsky](https://github.com/Semigradsky), and [Matt Pocock](https://github.com/mattpocock).
