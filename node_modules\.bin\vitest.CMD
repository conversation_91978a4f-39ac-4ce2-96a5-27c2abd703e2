@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\Projects\valtab\node_modules\.pnpm\vitest@1.6.1\node_modules\vitest\node_modules;D:\Projects\valtab\node_modules\.pnpm\vitest@1.6.1\node_modules;D:\Projects\valtab\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\Projects\valtab\node_modules\.pnpm\vitest@1.6.1\node_modules\vitest\node_modules;D:\Projects\valtab\node_modules\.pnpm\vitest@1.6.1\node_modules;D:\Projects\valtab\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\vitest\vitest.mjs" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\vitest\vitest.mjs" %*
)
