import { proxy, subscribe } from 'valtio'

interface StoreManager<T extends object> {
  store: T
  channel: BroadcastChannel
  cleanup: () => void
}

const storeRegistry = new Map<string, StoreManager<any>>()

export const createSharedStore = <T extends object>(
  initialState: T,
  storeName: string
): T => {
  // Return existing store if already created
  if (storeRegistry.has(storeName)) {
    return storeRegistry.get(storeName)!.store
  }

  const store = proxy(initialState)

  // Request current state from other tabs
  const syncChannel = new BroadcastChannel(`valtio-shared-${storeName}`)
  let isInitialized = false

  // Request state from existing tabs
  syncChannel.postMessage({ type: 'STATE_REQUEST' })

  // Set a timeout to proceed if no response (first tab)
  const initTimeout = setTimeout(() => {
    if (!isInitialized) {
      isInitialized = true
    }
  }, 100)

  // Listen for state responses and requests
  const handleSyncMessage = (event: MessageEvent) => {
    if (event.data.type === 'STATE_RESPONSE' && !isInitialized) {
      Object.assign(store, event.data.state)
      isInitialized = true
      clearTimeout(initTimeout)
    } else if (event.data.type === 'STATE_REQUEST' && isInitialized) {
      // Send current state to requesting tab
      syncChannel.postMessage({
        type: 'STATE_RESPONSE',
        state: JSON.parse(JSON.stringify(store))
      })
    }
  }

  syncChannel.addEventListener('message', handleSyncMessage)

  const channel = new BroadcastChannel(`valtio-shared-${storeName}`)

  // Subscribe to store changes and broadcast to other tabs
  const unsubscribe = subscribe(store, () => {
    channel.postMessage({
      type: 'STATE_UPDATE',
      state: JSON.parse(JSON.stringify(store))
    })
  })

  // Listen for updates from other tabs
  const handleMessage = (event: MessageEvent) => {
    if (event.data.type === 'STATE_UPDATE') {
      Object.assign(store, event.data.state)
    }
  }

  channel.addEventListener('message', handleMessage)

  const cleanup = () => {
    unsubscribe()
    channel.removeEventListener('message', handleMessage)
    channel.close()
    syncChannel.removeEventListener('message', handleSyncMessage)
    syncChannel.close()
    storeRegistry.delete(storeName)
  }

  // Cleanup on page unload
  window.addEventListener('beforeunload', cleanup)

  storeRegistry.set(storeName, { store, channel, cleanup })

  return store
}

export const destroySharedStore = (storeName: string) => {
  const manager = storeRegistry.get(storeName)
  if (manager) {
    manager.cleanup()
  }
}
